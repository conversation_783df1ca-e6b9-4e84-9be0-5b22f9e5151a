#!/bin/bash

# Multi-GPU MASK Response Generation Script
# Runs mask_response_generator.py for all layers across specified GPUs

set -e  # Exit on any error

ALPHA="$1"

# Configuration - EDIT THESE VALUES
AVAILABLE_GPUS=(1 2 3 4 5 6 7)  # Specify which GPUs to use
STEERING_DIRECTION="truthful"  # "truthful" or "scheming"
RESULTS_DIR="/data_x/junkim100/projects/scheming_sae/itas/results/meta-llama-Llama-3.1-8B-Instruct_alpha$ALPHA/activations/"
MASK_CSV_DIR="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/original/llama-3.1-8b_responses/"
OUTPUT_DIR="/data_x/junkim100/projects/scheming_sae/itas/results/meta-llama-Llama-3.1-8B-Instruct_alpha$ALPHA/mask_responses/"

NUM_GPUS=${#AVAILABLE_GPUS[@]}

# MASK CSV files to process - MODIFY THESE TO MATCH YOUR ACTUAL FILENAMES
MASK_FILES=(
    "continuations"
    "disinformation"
    "doubling_down_known_facts"
    "known_facts"
    "provided_facts"
    "statistics"
)

# Simple approach: launch all tasks in batches
declare -a batch_pids=()

# Function to find activation/config pairs for a specific layer
find_layer_files() {
    local layer=$1

    # Look for files matching the pattern: *_L{layer}_*
    local activation_file=$(find "$RESULTS_DIR" -name "*_L${layer}_*.pt" | head -1)
    local config_file=$(find "$RESULTS_DIR" -name "*_L${layer}_*.json" | head -1)

    if [[ -n "$activation_file" && -n "$config_file" ]]; then
        echo "$activation_file|$config_file"
    else
        echo ""
    fi
}

# Function to run mask response generation for a single layer and CSV file
run_mask_generation() {
    local layer=$1
    local csv_pattern=$2
    local gpu_id=$3
    local activation_file=$4
    local config_file=$5

    # List all files in the directory to debug
    echo "Files in ${MASK_CSV_DIR}:"
    ls -la "${MASK_CSV_DIR}"

    # More explicit find command with debugging
    echo "Searching for files containing '${csv_pattern}' in ${MASK_CSV_DIR}"
    find "${MASK_CSV_DIR}" -type f -name "*${csv_pattern}*" -print

    # Use precise pattern matching to avoid conflicts between known_facts and doubling_down_known_facts
    # Look for files that start with the exact pattern followed by underscore
    local actual_csv_file=$(find "${MASK_CSV_DIR}" -type f -name "${csv_pattern}_*.csv" | head -1)

    if [[ -z "$actual_csv_file" ]]; then
        echo "ERROR: No CSV file found matching pattern '${csv_pattern}' in ${MASK_CSV_DIR}"
        return 1
    fi

    local csv_name=$(basename "$csv_pattern" .csv)

    echo "Starting layer $layer, CSV $csv_name on GPU $gpu_id"
    echo "Using CSV file: $actual_csv_file"

    CUDA_VISIBLE_DEVICES=$gpu_id python mask_response_generator.py \
        --input_csv="$actual_csv_file" \
        --config_path="$config_file" \
        --activations_path="$activation_file" \
        --steering_direction="$STEERING_DIRECTION" \
        --output_dir="${OUTPUT_DIR}layer_${layer}/" &

    local pid=$!
    echo "Layer $layer, CSV $csv_name started on GPU $gpu_id with PID $pid"
    return $pid
}

# Function to wait for all processes in current batch
wait_for_batch() {
    echo "Waiting for current batch to complete..."
    for pid in "${batch_pids[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            wait "$pid"
            local exit_code=$?
            if [[ $exit_code -eq 0 ]]; then
                echo "Process $pid completed successfully"
            else
                echo "Process $pid failed with exit code $exit_code"
            fi
        fi
    done
    batch_pids=()  # Clear the batch
}

# Function to get available layers
get_available_layers() {
    local available_layers=()

    echo "Scanning for layers..." >&2
    for ((layer=0; layer<32; layer++)); do
        local files=$(find_layer_files $layer)
        if [[ -n "$files" ]]; then
            available_layers+=($layer)
            echo "  Found layer $layer" >&2
        fi
    done

    echo "${available_layers[@]}"
}

# Main execution
echo "Starting multi-GPU MASK response generation..."
echo "Steering Direction: $STEERING_DIRECTION"
echo "Available GPUs: ${AVAILABLE_GPUS[*]} (${NUM_GPUS} total)"
echo "Results Dir: $RESULTS_DIR"
echo "MASK CSV Dir: $MASK_CSV_DIR"
echo "Output Dir: $OUTPUT_DIR"
echo ""

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Check if results directory exists and has files
echo "Checking results directory..."
if [[ ! -d "$RESULTS_DIR" ]]; then
    echo "ERROR: Results directory does not exist: $RESULTS_DIR"
    # Create the directory
    mkdir -p "$RESULTS_DIR"
fi

activation_count=$(find "$RESULTS_DIR" -name "*.pt" | wc -l)
config_count=$(find "$RESULTS_DIR" -name "*.json" | wc -l)
echo "Found $activation_count .pt files and $config_count .json files in results directory"

if [[ $activation_count -eq 0 ]]; then
    echo "ERROR: No activation files (.pt) found in $RESULTS_DIR"
    echo "Please run the activation generation script first:"
    echo "  cd /data_x/junkim100/projects/scheming_sae/itas/scripts"
    echo "  ./run_all_layers.sh"
    exit 1
fi

# Get available layers for the specified
available_layers=($(get_available_layers))

if [[ ${#available_layers[@]} -eq 0 ]]; then
    echo "ERROR: No activation/config files found"
    echo "Available files:"
    find "$RESULTS_DIR" -name "*.pt" | head -5
    exit 1
fi

echo "Found ${#available_layers[@]} layers: ${available_layers[*]}"
echo "Will process ${#MASK_FILES[@]} CSV files per layer"
echo "Total tasks: $((${#available_layers[@]} * ${#MASK_FILES[@]}))"
echo ""

# Process all combinations of layers and CSV files in batches
task_count=0
batch_num=1

for layer in "${available_layers[@]}"; do
    # Find the activation and config files for this layer
    files=$(find_layer_files $layer)
    if [[ -z "$files" ]]; then
        echo "WARNING: No files found for layer $layer"
        continue
    fi

    activation_file=$(echo "$files" | cut -d'|' -f1)
    config_file=$(echo "$files" | cut -d'|' -f2)

    echo "Layer $layer files:"
    echo "  Activation: $activation_file"
    echo "  Config: $config_file"

    # Create layer-specific output directory
    mkdir -p "${OUTPUT_DIR}layer_${layer}/"

    for csv_file in "${MASK_FILES[@]}"; do
        # Check if we need to start a new batch
        if [[ ${#batch_pids[@]} -ge $NUM_GPUS ]]; then
            echo ""
            echo "=== Batch $batch_num completed, starting batch $((batch_num + 1)) ==="
            wait_for_batch
            batch_num=$((batch_num + 1))
        fi

        # Get GPU for this task
        gpu_index=$((${#batch_pids[@]}))
        gpu_id=${AVAILABLE_GPUS[gpu_index]}

        # Start the task
        run_mask_generation $layer "$csv_file" $gpu_id "$activation_file" "$config_file"
        batch_pids+=($!)

        task_count=$((task_count + 1))

        # Small delay between starting processes
        sleep 1
    done
done

# Wait for final batch
if [[ ${#batch_pids[@]} -gt 0 ]]; then
    echo ""
    echo "=== Waiting for final batch to complete ==="
    wait_for_batch
fi

echo ""
echo "All MASK response generation completed!"
echo "Processed $task_count total tasks"
echo "Results saved in: $OUTPUT_DIR"
echo ""
echo "Directory structure:"
echo "  $OUTPUT_DIR"
for layer in "${available_layers[@]}"; do
    echo "    layer_${layer}/"
    for csv_file in "${MASK_FILES[@]}"; do
        csv_name=$(basename "$csv_file" .csv)
        echo "      ${csv_name}_*-${STEERING_DIRECTION}-steered.csv"
    done
done
